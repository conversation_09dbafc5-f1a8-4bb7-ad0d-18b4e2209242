{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/database.ts"], "sourcesContent": ["import { SupabaseClient } from '@supabase/supabase-js'\nimport { \n  Project, \n  BlogSeries, \n  Author, \n  BlogPost, \n  SEOSettings, \n  GenerationHistory,\n  DatabaseOperations \n} from '@/types/database'\n\nexport class SupabaseDatabaseService implements DatabaseOperations {\n  constructor(private supabase: SupabaseClient) {}\n\n  // 项目管理\n  async getProjects(): Promise<Project[]> {\n    const { data, error } = await this.supabase\n      .from('projects')\n      .select('*')\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data || []\n  }\n\n  async getProject(id: string): Promise<Project | null> {\n    const { data, error } = await this.supabase\n      .from('projects')\n      .select('*')\n      .eq('id', id)\n      .single()\n    \n    if (error) {\n      if (error.code === 'PGRST116') return null // Not found\n      throw error\n    }\n    return data\n  }\n\n  async createProject(project: Omit<Project, 'id' | 'created_at' | 'updated_at'>): Promise<Project> {\n    const { data, error } = await this.supabase\n      .from('projects')\n      .insert(project)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateProject(id: string, updates: Partial<Project>): Promise<Project> {\n    const { data, error } = await this.supabase\n      .from('projects')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async deleteProject(id: string): Promise<void> {\n    const { error } = await this.supabase\n      .from('projects')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  }\n\n  // 博文系列\n  async getBlogSeries(projectId: string): Promise<BlogSeries[]> {\n    const { data, error } = await this.supabase\n      .from('blog_series')\n      .select('*')\n      .eq('project_id', projectId)\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data || []\n  }\n\n  async getBlogSeriesById(id: string): Promise<BlogSeries | null> {\n    const { data, error } = await this.supabase\n      .from('blog_series')\n      .select('*')\n      .eq('id', id)\n      .single()\n    \n    if (error) {\n      if (error.code === 'PGRST116') return null\n      throw error\n    }\n    return data\n  }\n\n  async createBlogSeries(series: Omit<BlogSeries, 'id' | 'created_at' | 'updated_at'>): Promise<BlogSeries> {\n    const { data, error } = await this.supabase\n      .from('blog_series')\n      .insert(series)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateBlogSeries(id: string, updates: Partial<BlogSeries>): Promise<BlogSeries> {\n    const { data, error } = await this.supabase\n      .from('blog_series')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async deleteBlogSeries(id: string): Promise<void> {\n    const { error } = await this.supabase\n      .from('blog_series')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  }\n\n  // 作者管理\n  async getAuthors(projectId: string): Promise<Author[]> {\n    const { data, error } = await this.supabase\n      .from('authors')\n      .select('*')\n      .eq('project_id', projectId)\n      .order('created_at', { ascending: false })\n    \n    if (error) throw error\n    return data || []\n  }\n\n  async getAuthor(id: string): Promise<Author | null> {\n    const { data, error } = await this.supabase\n      .from('authors')\n      .select('*')\n      .eq('id', id)\n      .single()\n    \n    if (error) {\n      if (error.code === 'PGRST116') return null\n      throw error\n    }\n    return data\n  }\n\n  async createAuthor(author: Omit<Author, 'id' | 'created_at' | 'updated_at'>): Promise<Author> {\n    const { data, error } = await this.supabase\n      .from('authors')\n      .insert(author)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateAuthor(id: string, updates: Partial<Author>): Promise<Author> {\n    const { data, error } = await this.supabase\n      .from('authors')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async deleteAuthor(id: string): Promise<void> {\n    const { error } = await this.supabase\n      .from('authors')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  }\n\n  // 博文管理\n  async getBlogPosts(projectId: string, filters?: {\n    status?: BlogPost['status']\n    seriesId?: string\n    authorId?: string\n    limit?: number\n    offset?: number\n  }): Promise<BlogPost[]> {\n    let query = this.supabase\n      .from('blog_posts')\n      .select(`\n        *,\n        series:blog_series(id, name),\n        author:authors(id, name)\n      `)\n      .eq('project_id', projectId)\n    \n    if (filters?.status) {\n      query = query.eq('status', filters.status)\n    }\n    if (filters?.seriesId) {\n      query = query.eq('series_id', filters.seriesId)\n    }\n    if (filters?.authorId) {\n      query = query.eq('author_id', filters.authorId)\n    }\n    if (filters?.limit) {\n      query = query.limit(filters.limit)\n    }\n    if (filters?.offset) {\n      query = query.range(filters.offset, (filters.offset + (filters.limit || 10)) - 1)\n    }\n    \n    query = query.order('created_at', { ascending: false })\n    \n    const { data, error } = await query\n    \n    if (error) throw error\n    return data || []\n  }\n\n  async getBlogPost(id: string): Promise<BlogPost | null> {\n    const { data, error } = await this.supabase\n      .from('blog_posts')\n      .select(`\n        *,\n        series:blog_series(id, name),\n        author:authors(id, name)\n      `)\n      .eq('id', id)\n      .single()\n    \n    if (error) {\n      if (error.code === 'PGRST116') return null\n      throw error\n    }\n    return data\n  }\n\n  async createBlogPost(post: Omit<BlogPost, 'id' | 'created_at' | 'updated_at'>): Promise<BlogPost> {\n    // 计算字数和阅读时间\n    const wordCount = post.content.length\n    const readingTime = Math.ceil(wordCount / 200) // 假设每分钟200字\n    \n    const postWithMeta = {\n      ...post,\n      word_count: wordCount,\n      reading_time: readingTime\n    }\n    \n    const { data, error } = await this.supabase\n      .from('blog_posts')\n      .insert(postWithMeta)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateBlogPost(id: string, updates: Partial<BlogPost>): Promise<BlogPost> {\n    // 如果更新了内容，重新计算字数和阅读时间\n    if (updates.content) {\n      const wordCount = updates.content.length\n      const readingTime = Math.ceil(wordCount / 200)\n      updates.word_count = wordCount\n      updates.reading_time = readingTime\n    }\n    \n    const { data, error } = await this.supabase\n      .from('blog_posts')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async deleteBlogPost(id: string): Promise<void> {\n    const { error } = await this.supabase\n      .from('blog_posts')\n      .delete()\n      .eq('id', id)\n    \n    if (error) throw error\n  }\n\n  // SEO设置\n  async getSEOSettings(projectId: string): Promise<SEOSettings | null> {\n    const { data, error } = await this.supabase\n      .from('seo_settings')\n      .select('*')\n      .eq('project_id', projectId)\n      .single()\n    \n    if (error) {\n      if (error.code === 'PGRST116') return null\n      throw error\n    }\n    return data\n  }\n\n  async createSEOSettings(settings: Omit<SEOSettings, 'id' | 'created_at' | 'updated_at'>): Promise<SEOSettings> {\n    const { data, error } = await this.supabase\n      .from('seo_settings')\n      .insert(settings)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateSEOSettings(id: string, updates: Partial<SEOSettings>): Promise<SEOSettings> {\n    const { data, error } = await this.supabase\n      .from('seo_settings')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  // 生成历史\n  async getGenerationHistory(projectId: string, limit: number = 50): Promise<GenerationHistory[]> {\n    const { data, error } = await this.supabase\n      .from('generation_history')\n      .select('*')\n      .eq('project_id', projectId)\n      .order('created_at', { ascending: false })\n      .limit(limit)\n    \n    if (error) throw error\n    return data || []\n  }\n\n  async createGenerationHistory(history: Omit<GenerationHistory, 'id' | 'created_at'>): Promise<GenerationHistory> {\n    const { data, error } = await this.supabase\n      .from('generation_history')\n      .insert(history)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n\n  async updateGenerationHistory(id: string, updates: Partial<GenerationHistory>): Promise<GenerationHistory> {\n    const { data, error } = await this.supabase\n      .from('generation_history')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single()\n    \n    if (error) throw error\n    return data\n  }\n}\n"], "names": [], "mappings": ";;;;;AAWO,MAAM;IAGX,OAAO;IACP,MAAM,cAAkC;QACtC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,MAAM,WAAW,EAAU,EAA2B;QACpD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO,KAAK,YAAY;;YACvD,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,cAAc,OAA0D,EAAoB;QAChG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,cAAc,EAAU,EAAE,OAAyB,EAAoB;QAC3E,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,YACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,cAAc,EAAU,EAAiB;QAC7C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,YACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,MAAM,cAAc,SAAiB,EAAyB;QAC5D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,MAAM,kBAAkB,EAAU,EAA8B;QAC9D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,eACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO;YACtC,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB,MAA4D,EAAuB;QACxG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,eACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,iBAAiB,EAAU,EAAE,OAA4B,EAAuB;QACpF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,eACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,iBAAiB,EAAU,EAAiB;QAChD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,eACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,MAAM,WAAW,SAAiB,EAAqB;QACrD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAE1C,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,MAAM,UAAU,EAAU,EAA0B;QAClD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,WACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO;YACtC,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,aAAa,MAAwD,EAAmB;QAC5F,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,WACL,MAAM,CAAC,QACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,EAAU,EAAE,OAAwB,EAAmB;QACxE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,WACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,aAAa,EAAU,EAAiB;QAC5C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,WACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,OAAO;IACP,MAAM,aAAa,SAAiB,EAAE,OAMrC,EAAuB;QACtB,IAAI,QAAQ,IAAI,CAAC,QAAQ,CACtB,IAAI,CAAC,cACL,MAAM,CAAE,iGAKR,EAAE,CAAC,cAAc;QAEpB,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE;YACnB,QAAQ,MAAM,EAAE,CAAC,UAAU,QAAQ,MAAM;QAC3C;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QAChD;QACA,IAAI,oBAAA,8BAAA,QAAS,QAAQ,EAAE;YACrB,QAAQ,MAAM,EAAE,CAAC,aAAa,QAAQ,QAAQ;QAChD;QACA,IAAI,oBAAA,8BAAA,QAAS,KAAK,EAAE;YAClB,QAAQ,MAAM,KAAK,CAAC,QAAQ,KAAK;QACnC;QACA,IAAI,oBAAA,8BAAA,QAAS,MAAM,EAAE;YACnB,QAAQ,MAAM,KAAK,CAAC,QAAQ,MAAM,EAAE,AAAC,QAAQ,MAAM,GAAG,CAAC,QAAQ,KAAK,IAAI,EAAE,IAAK;QACjF;QAEA,QAAQ,MAAM,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM;QAErD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM;QAE9B,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,MAAM,YAAY,EAAU,EAA4B;QACtD,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,cACL,MAAM,CAAE,iGAKR,EAAE,CAAC,MAAM,IACT,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO;YACtC,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,eAAe,IAAwD,EAAqB;QAChG,YAAY;QACZ,MAAM,YAAY,KAAK,OAAO,CAAC,MAAM;QACrC,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY,KAAK,YAAY;;QAE3D,MAAM,eAAe;YACnB,GAAG,IAAI;YACP,YAAY;YACZ,cAAc;QAChB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,cACL,MAAM,CAAC,cACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAe,EAAU,EAAE,OAA0B,EAAqB;QAC9E,sBAAsB;QACtB,IAAI,QAAQ,OAAO,EAAE;YACnB,MAAM,YAAY,QAAQ,OAAO,CAAC,MAAM;YACxC,MAAM,cAAc,KAAK,IAAI,CAAC,YAAY;YAC1C,QAAQ,UAAU,GAAG;YACrB,QAAQ,YAAY,GAAG;QACzB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,cACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,eAAe,EAAU,EAAiB;QAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAClC,IAAI,CAAC,cACL,MAAM,GACN,EAAE,CAAC,MAAM;QAEZ,IAAI,OAAO,MAAM;IACnB;IAEA,QAAQ;IACR,MAAM,eAAe,SAAiB,EAA+B;QACnE,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,gBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,MAAM;QAET,IAAI,OAAO;YACT,IAAI,MAAM,IAAI,KAAK,YAAY,OAAO;YACtC,MAAM;QACR;QACA,OAAO;IACT;IAEA,MAAM,kBAAkB,QAA+D,EAAwB;QAC7G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,gBACL,MAAM,CAAC,UACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,kBAAkB,EAAU,EAAE,OAA6B,EAAwB;QACvF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,gBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,OAAO;IACP,MAAM,qBAAqB,SAAiB,EAAoD;YAAlD,QAAA,iEAAgB;QAC5D,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,KACP,EAAE,CAAC,cAAc,WACjB,KAAK,CAAC,cAAc;YAAE,WAAW;QAAM,GACvC,KAAK,CAAC;QAET,IAAI,OAAO,MAAM;QACjB,OAAO,QAAQ,EAAE;IACnB;IAEA,MAAM,wBAAwB,OAAqD,EAA8B;QAC/G,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,SACP,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IAEA,MAAM,wBAAwB,EAAU,EAAE,OAAmC,EAA8B;QACzG,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CACxC,IAAI,CAAC,sBACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,IACT,MAAM,GACN,MAAM;QAET,IAAI,OAAO,MAAM;QACjB,OAAO;IACT;IApWA,YAAY,AAAQ,QAAwB,CAAE;;aAA1B,WAAA;IAA2B;AAqWjD", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\n// AI博文数据库 (主数据库)\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// tarot-seo项目数据库\nconst tarotSeoUrl = process.env.TAROT_SEO_SUPABASE_URL!\nconst tarotSeoAnonKey = process.env.TAROT_SEO_SUPABASE_ANON_KEY!\n\nexport const tarotSeoSupabase = createClient(tarotSeoUrl, tarotSeoAnonKey)\n\n// 项目配置类型\nexport interface ProjectConfig {\n  id: string\n  name: string\n  supabaseUrl: string\n  supabaseAnonKey: string\n  supabaseServiceKey: string\n}\n\n// 默认项目配置\nexport const defaultProjects: ProjectConfig[] = [\n  {\n    id: 'tarot-seo',\n    name: 'Tarot SEO',\n    supabaseUrl: process.env.TAROT_SEO_SUPABASE_URL!,\n    supabaseAnonKey: process.env.TAROT_SEO_SUPABASE_ANON_KEY!,\n    supabaseServiceKey: process.env.TAROT_SEO_SUPABASE_SERVICE_ROLE_KEY!,\n  }\n]\n\n// 根据项目ID获取Supabase客户端\nexport function getProjectSupabase(projectId: string) {\n  const project = defaultProjects.find(p => p.id === projectId)\n  if (!project) {\n    throw new Error(`Project ${projectId} not found`)\n  }\n  \n  return createClient(project.supabaseUrl, project.supabaseAnonKey)\n}\n"], "names": [], "mappings": ";;;;;;AAGoB;AAHpB;;AAEA,iBAAiB;AACjB,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAElD,iBAAiB;AACjB,MAAM,cAAc,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;AACtD,MAAM,kBAAkB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B;AAExD,MAAM,mBAAmB,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAYnD,MAAM,kBAAmC;IAC9C;QACE,IAAI;QACJ,MAAM;QACN,aAAa,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB;QAC/C,iBAAiB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,2BAA2B;QACxD,oBAAoB,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mCAAmC;IACrE;CACD;AAGM,SAAS,mBAAmB,SAAiB;IAClD,MAAM,UAAU,gBAAgB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACnD,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM,AAAC,WAAoB,OAAV,WAAU;IACvC;IAEA,OAAO,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,WAAW,EAAE,QAAQ,eAAe;AAClE", "debugId": null}}, {"offset": {"line": 261, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/contexts/ProjectContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { SupabaseDatabaseService } from '@/lib/database'\nimport { getProjectSupabase, defaultProjects } from '@/lib/supabase'\nimport { Project } from '@/types/database'\n\ninterface ProjectContextType {\n  currentProject: Project | null\n  projects: Project[]\n  databaseService: SupabaseDatabaseService | null\n  switchProject: (projectId: string) => void\n  refreshProjects: () => Promise<void>\n  loading: boolean\n  error: string | null\n}\n\nconst ProjectContext = createContext<ProjectContextType | undefined>(undefined)\n\ninterface ProjectProviderProps {\n  children: ReactNode\n}\n\nexport function ProjectProvider({ children }: ProjectProviderProps) {\n  const [currentProject, setCurrentProject] = useState<Project | null>(null)\n  const [projects, setProjects] = useState<Project[]>([])\n  const [databaseService, setDatabaseService] = useState<SupabaseDatabaseService | null>(null)\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  // 初始化项目列表\n  const refreshProjects = async () => {\n    try {\n      setLoading(true)\n      setError(null)\n      \n      // 从默认配置加载项目（后续可以从主数据库加载）\n      const projectList: Project[] = defaultProjects.map(p => ({\n        id: p.id,\n        name: p.name,\n        description: `${p.name} 项目`,\n        supabase_url: p.supabaseUrl,\n        supabase_anon_key: p.supabaseAnonKey,\n        supabase_service_key: p.supabaseServiceKey,\n        created_at: new Date().toISOString(),\n        updated_at: new Date().toISOString()\n      }))\n      \n      setProjects(projectList)\n      \n      // 如果没有当前项目，设置默认项目\n      if (!currentProject && projectList.length > 0) {\n        const defaultProject = projectList.find(p => p.id === 'tarot-seo') || projectList[0]\n        setCurrentProject(defaultProject)\n        \n        // 创建数据库服务实例\n        const supabase = getProjectSupabase(defaultProject.id)\n        setDatabaseService(new SupabaseDatabaseService(supabase))\n      }\n      \n    } catch (err) {\n      setError(err instanceof Error ? err.message : '加载项目失败')\n      console.error('加载项目失败:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  // 切换项目\n  const switchProject = (projectId: string) => {\n    const project = projects.find(p => p.id === projectId)\n    if (!project) {\n      setError(`项目 ${projectId} 不存在`)\n      return\n    }\n    \n    try {\n      setCurrentProject(project)\n      \n      // 创建新的数据库服务实例\n      const supabase = getProjectSupabase(projectId)\n      setDatabaseService(new SupabaseDatabaseService(supabase))\n      \n      // 保存到localStorage\n      localStorage.setItem('currentProjectId', projectId)\n      \n      setError(null)\n    } catch (err) {\n      setError(err instanceof Error ? err.message : '切换项目失败')\n      console.error('切换项目失败:', err)\n    }\n  }\n\n  // 初始化\n  useEffect(() => {\n    const initializeProject = async () => {\n      await refreshProjects()\n      \n      // 从localStorage恢复上次选择的项目\n      const savedProjectId = localStorage.getItem('currentProjectId')\n      if (savedProjectId && projects.length > 0) {\n        const savedProject = projects.find(p => p.id === savedProjectId)\n        if (savedProject) {\n          switchProject(savedProjectId)\n        }\n      }\n    }\n    \n    initializeProject()\n  }, [])\n\n  const value: ProjectContextType = {\n    currentProject,\n    projects,\n    databaseService,\n    switchProject,\n    refreshProjects,\n    loading,\n    error\n  }\n\n  return (\n    <ProjectContext.Provider value={value}>\n      {children}\n    </ProjectContext.Provider>\n  )\n}\n\nexport function useProject() {\n  const context = useContext(ProjectContext)\n  if (context === undefined) {\n    throw new Error('useProject must be used within a ProjectProvider')\n  }\n  return context\n}\n\n// 便捷的hook，用于获取数据库服务\nexport function useDatabase() {\n  const { databaseService, currentProject, loading } = useProject()\n  \n  if (loading) {\n    return { databaseService: null, currentProject: null, loading: true }\n  }\n  \n  if (!databaseService || !currentProject) {\n    throw new Error('数据库服务未初始化或没有选择项目')\n  }\n  \n  return { databaseService, currentProject, loading: false }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBA,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAkC;AAM9D,SAAS,gBAAgB,KAAkC;QAAlC,EAAE,QAAQ,EAAwB,GAAlC;;IAC9B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkC;IACvF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,UAAU;IACV,MAAM,kBAAkB;QACtB,IAAI;YACF,WAAW;YACX,SAAS;YAET,yBAAyB;YACzB,MAAM,cAAyB,yHAAA,CAAA,kBAAe,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;oBACvD,IAAI,EAAE,EAAE;oBACR,MAAM,EAAE,IAAI;oBACZ,aAAa,AAAC,GAAS,OAAP,EAAE,IAAI,EAAC;oBACvB,cAAc,EAAE,WAAW;oBAC3B,mBAAmB,EAAE,eAAe;oBACpC,sBAAsB,EAAE,kBAAkB;oBAC1C,YAAY,IAAI,OAAO,WAAW;oBAClC,YAAY,IAAI,OAAO,WAAW;gBACpC,CAAC;YAED,YAAY;YAEZ,kBAAkB;YAClB,IAAI,CAAC,kBAAkB,YAAY,MAAM,GAAG,GAAG;gBAC7C,MAAM,iBAAiB,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,gBAAgB,WAAW,CAAC,EAAE;gBACpF,kBAAkB;gBAElB,YAAY;gBACZ,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,EAAE;gBACrD,mBAAmB,IAAI,yHAAA,CAAA,0BAAuB,CAAC;YACjD;QAEF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,WAAW;QAC3B,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;IACP,MAAM,gBAAgB,CAAC;QACrB,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC5C,IAAI,CAAC,SAAS;YACZ,SAAS,AAAC,MAAe,OAAV,WAAU;YACzB;QACF;QAEA,IAAI;YACF,kBAAkB;YAElB,cAAc;YACd,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,qBAAkB,AAAD,EAAE;YACpC,mBAAmB,IAAI,yHAAA,CAAA,0BAAuB,CAAC;YAE/C,kBAAkB;YAClB,aAAa,OAAO,CAAC,oBAAoB;YAEzC,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,WAAW;QAC3B;IACF;IAEA,MAAM;IACN,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;+DAAoB;oBACxB,MAAM;oBAEN,yBAAyB;oBACzB,MAAM,iBAAiB,aAAa,OAAO,CAAC;oBAC5C,IAAI,kBAAkB,SAAS,MAAM,GAAG,GAAG;wBACzC,MAAM,eAAe,SAAS,IAAI;wFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;wBACjD,IAAI,cAAc;4BAChB,cAAc;wBAChB;oBACF;gBACF;;YAEA;QACF;oCAAG,EAAE;IAEL,MAAM,QAA4B;QAChC;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;kBAC7B;;;;;;AAGP;GAvGgB;KAAA;AAyGT,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;IANgB;AAST,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG;IAErD,IAAI,SAAS;QACX,OAAO;YAAE,iBAAiB;YAAM,gBAAgB;YAAM,SAAS;QAAK;IACtE;IAEA,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACvC,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;QAAE;QAAiB;QAAgB,SAAS;IAAM;AAC3D;IAZgB;;QACuC", "debugId": null}}]}