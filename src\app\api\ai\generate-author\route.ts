import { NextRequest, NextResponse } from 'next/server'
import { aiService } from '@/lib/ai'
import { z } from 'zod'

const generateAuthorSchema = z.object({
  topic: z.string().min(1, '话题不能为空'),
  language: z.string().default('zh')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证请求参数
    const validatedData = generateAuthorSchema.parse(body)
    
    // 调用AI服务生成作者信息
    const result = await aiService.generateAuthorInfo({
      topic: validatedData.topic,
      language: validatedData.language
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('作者信息生成失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '作者信息生成失败'
    }, { status: 500 })
  }
}
