import { NextRequest, NextResponse } from 'next/server'
import { aiService } from '@/lib/ai'
import { z } from 'zod'

// 请求参数验证schema
const generateBlogSchema = z.object({
  keywords: z.array(z.string()).min(1, '至少需要一个关键词'),
  topic: z.string().optional(),
  title: z.string().optional(),
  language: z.string().default('zh'),
  seriesContext: z.string().optional(),
  customPrompt: z.string().optional(),
  aiModel: z.enum(['gpt-4', 'gpt-3.5-turbo', 'qwen-plus-latest']).default('gpt-4')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证请求参数
    const validatedData = generateBlogSchema.parse(body)
    
    // 调用AI服务生成博文
    const result = await aiService.generateBlogPost({
      keywords: validatedData.keywords,
      topic: validatedData.topic,
      title: validatedData.title,
      language: validatedData.language,
      seriesContext: validatedData.seriesContext,
      customPrompt: validatedData.customPrompt
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('博文生成失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '博文生成失败'
    }, { status: 500 })
  }
}

// 生成标题的API
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    const schema = z.object({
      keywords: z.array(z.string()).min(1),
      topic: z.string().optional(),
      language: z.string().default('zh')
    })
    
    const validatedData = schema.parse(body)
    
    const title = await aiService.generateTitle({
      keywords: validatedData.keywords,
      topic: validatedData.topic,
      language: validatedData.language
    })
    
    return NextResponse.json({
      success: true,
      data: { title }
    })
    
  } catch (error) {
    console.error('标题生成失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '标题生成失败'
    }, { status: 500 })
  }
}
