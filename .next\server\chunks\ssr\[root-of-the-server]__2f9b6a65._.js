module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/lib/database.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "SupabaseDatabaseService": ()=>SupabaseDatabaseService
});
class SupabaseDatabaseService {
    supabase;
    constructor(supabase){
        this.supabase = supabase;
    }
    // 项目管理
    async getProjects() {
        const { data, error } = await this.supabase.from('projects').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getProject(id) {
        const { data, error } = await this.supabase.from('projects').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null // Not found
            ;
            throw error;
        }
        return data;
    }
    async createProject(project) {
        const { data, error } = await this.supabase.from('projects').insert(project).select().single();
        if (error) throw error;
        return data;
    }
    async updateProject(id, updates) {
        const { data, error } = await this.supabase.from('projects').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteProject(id) {
        const { error } = await this.supabase.from('projects').delete().eq('id', id);
        if (error) throw error;
    }
    // 博文系列
    async getBlogSeries(projectId) {
        const { data, error } = await this.supabase.from('blog_series').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getBlogSeriesById(id) {
        const { data, error } = await this.supabase.from('blog_series').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createBlogSeries(series) {
        const { data, error } = await this.supabase.from('blog_series').insert(series).select().single();
        if (error) throw error;
        return data;
    }
    async updateBlogSeries(id, updates) {
        const { data, error } = await this.supabase.from('blog_series').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteBlogSeries(id) {
        const { error } = await this.supabase.from('blog_series').delete().eq('id', id);
        if (error) throw error;
    }
    // 作者管理
    async getAuthors(projectId) {
        const { data, error } = await this.supabase.from('authors').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getAuthor(id) {
        const { data, error } = await this.supabase.from('authors').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createAuthor(author) {
        const { data, error } = await this.supabase.from('authors').insert(author).select().single();
        if (error) throw error;
        return data;
    }
    async updateAuthor(id, updates) {
        const { data, error } = await this.supabase.from('authors').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteAuthor(id) {
        const { error } = await this.supabase.from('authors').delete().eq('id', id);
        if (error) throw error;
    }
    // 博文管理
    async getBlogPosts(projectId, filters) {
        let query = this.supabase.from('blog_posts').select(`
        *,
        series:blog_series(id, name),
        author:authors(id, name)
      `).eq('project_id', projectId);
        if (filters?.status) {
            query = query.eq('status', filters.status);
        }
        if (filters?.seriesId) {
            query = query.eq('series_id', filters.seriesId);
        }
        if (filters?.authorId) {
            query = query.eq('author_id', filters.authorId);
        }
        if (filters?.limit) {
            query = query.limit(filters.limit);
        }
        if (filters?.offset) {
            query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
        }
        query = query.order('created_at', {
            ascending: false
        });
        const { data, error } = await query;
        if (error) throw error;
        return data || [];
    }
    async getBlogPost(id) {
        const { data, error } = await this.supabase.from('blog_posts').select(`
        *,
        series:blog_series(id, name),
        author:authors(id, name)
      `).eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createBlogPost(post) {
        // 计算字数和阅读时间
        const wordCount = post.content.length;
        const readingTime = Math.ceil(wordCount / 200) // 假设每分钟200字
        ;
        const postWithMeta = {
            ...post,
            word_count: wordCount,
            reading_time: readingTime
        };
        const { data, error } = await this.supabase.from('blog_posts').insert(postWithMeta).select().single();
        if (error) throw error;
        return data;
    }
    async updateBlogPost(id, updates) {
        // 如果更新了内容，重新计算字数和阅读时间
        if (updates.content) {
            const wordCount = updates.content.length;
            const readingTime = Math.ceil(wordCount / 200);
            updates.word_count = wordCount;
            updates.reading_time = readingTime;
        }
        const { data, error } = await this.supabase.from('blog_posts').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteBlogPost(id) {
        const { error } = await this.supabase.from('blog_posts').delete().eq('id', id);
        if (error) throw error;
    }
    // SEO设置
    async getSEOSettings(projectId) {
        const { data, error } = await this.supabase.from('seo_settings').select('*').eq('project_id', projectId).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createSEOSettings(settings) {
        const { data, error } = await this.supabase.from('seo_settings').insert(settings).select().single();
        if (error) throw error;
        return data;
    }
    async updateSEOSettings(id, updates) {
        const { data, error } = await this.supabase.from('seo_settings').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    // 生成历史
    async getGenerationHistory(projectId, limit = 50) {
        const { data, error } = await this.supabase.from('generation_history').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data || [];
    }
    async createGenerationHistory(history) {
        const { data, error } = await this.supabase.from('generation_history').insert(history).select().single();
        if (error) throw error;
        return data;
    }
    async updateGenerationHistory(id, updates) {
        const { data, error } = await this.supabase.from('generation_history').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
}
}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/punycode [external] (punycode, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("punycode", () => require("punycode"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[project]/src/lib/supabase.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "defaultProjects": ()=>defaultProjects,
    "getProjectSupabase": ()=>getProjectSupabase,
    "supabase": ()=>supabase,
    "tarotSeoSupabase": ()=>tarotSeoSupabase
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-ssr] (ecmascript) <locals>");
;
// AI博文数据库 (主数据库)
const supabaseUrl = ("TURBOPACK compile-time value", "https://ohcnehqjrqzjlgbmjcck.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9oY25laHFqcnF6amxnYm1qY2NrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNjIzMzYsImV4cCI6MjA2OTgzODMzNn0.sKrSvG0qQEmQqgnoqSv-5EmvWRQ4FQAvSN2Yi_MMo7o");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
// tarot-seo项目数据库
const tarotSeoUrl = process.env.TAROT_SEO_SUPABASE_URL;
const tarotSeoAnonKey = process.env.TAROT_SEO_SUPABASE_ANON_KEY;
const tarotSeoSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(tarotSeoUrl, tarotSeoAnonKey);
const defaultProjects = [
    {
        id: 'tarot-seo',
        name: 'Tarot SEO',
        supabaseUrl: process.env.TAROT_SEO_SUPABASE_URL,
        supabaseAnonKey: process.env.TAROT_SEO_SUPABASE_ANON_KEY,
        supabaseServiceKey: process.env.TAROT_SEO_SUPABASE_SERVICE_ROLE_KEY
    }
];
function getProjectSupabase(projectId) {
    const project = defaultProjects.find((p)=>p.id === projectId);
    if (!project) {
        throw new Error(`Project ${projectId} not found`);
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(project.supabaseUrl, project.supabaseAnonKey);
}
}),
"[project]/src/contexts/ProjectContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "ProjectProvider": ()=>ProjectProvider,
    "useDatabase": ()=>useDatabase,
    "useProject": ()=>useProject
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const ProjectContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ProjectProvider({ children }) {
    const [currentProject, setCurrentProject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [projects, setProjects] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [databaseService, setDatabaseService] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // 初始化项目列表
    const refreshProjects = async ()=>{
        try {
            setLoading(true);
            setError(null);
            // 从默认配置加载项目（后续可以从主数据库加载）
            const projectList = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["defaultProjects"].map((p)=>({
                    id: p.id,
                    name: p.name,
                    description: `${p.name} 项目`,
                    supabase_url: p.supabaseUrl,
                    supabase_anon_key: p.supabaseAnonKey,
                    supabase_service_key: p.supabaseServiceKey,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }));
            setProjects(projectList);
            // 如果没有当前项目，设置默认项目
            if (!currentProject && projectList.length > 0) {
                const defaultProject = projectList.find((p)=>p.id === 'tarot-seo') || projectList[0];
                setCurrentProject(defaultProject);
                // 创建数据库服务实例
                const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProjectSupabase"])(defaultProject.id);
                setDatabaseService(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SupabaseDatabaseService"](supabase));
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : '加载项目失败');
            console.error('加载项目失败:', err);
        } finally{
            setLoading(false);
        }
    };
    // 切换项目
    const switchProject = (projectId)=>{
        const project = projects.find((p)=>p.id === projectId);
        if (!project) {
            setError(`项目 ${projectId} 不存在`);
            return;
        }
        try {
            setCurrentProject(project);
            // 创建新的数据库服务实例
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getProjectSupabase"])(projectId);
            setDatabaseService(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SupabaseDatabaseService"](supabase));
            // 保存到localStorage
            localStorage.setItem('currentProjectId', projectId);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : '切换项目失败');
            console.error('切换项目失败:', err);
        }
    };
    // 初始化
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const initializeProject = async ()=>{
            await refreshProjects();
            // 从localStorage恢复上次选择的项目
            const savedProjectId = localStorage.getItem('currentProjectId');
            if (savedProjectId && projects.length > 0) {
                const savedProject = projects.find((p)=>p.id === savedProjectId);
                if (savedProject) {
                    switchProject(savedProjectId);
                }
            }
        };
        initializeProject();
    }, []);
    const value = {
        currentProject,
        projects,
        databaseService,
        switchProject,
        refreshProjects,
        loading,
        error
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(ProjectContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ProjectContext.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
}
function useProject() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ProjectContext);
    if (context === undefined) {
        throw new Error('useProject must be used within a ProjectProvider');
    }
    return context;
}
function useDatabase() {
    const { databaseService, currentProject, loading } = useProject();
    if (loading) {
        return {
            databaseService: null,
            currentProject: null,
            loading: true
        };
    }
    if (!databaseService || !currentProject) {
        throw new Error('数据库服务未初始化或没有选择项目');
    }
    return {
        databaseService,
        currentProject,
        loading: false
    };
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__2f9b6a65._.js.map