'use client'

import { useState, useEffect } from 'react'
import { useProject } from '@/contexts/ProjectContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ProjectSelector } from '@/components/ProjectSelector'
import { Settings, Database, Globe, Key, Save, RefreshCw } from 'lucide-react'
import { SEOSettings } from '@/types/database'
import { useDatabase } from '@/contexts/ProjectContext'

export default function SettingsPage() {
  const { currentProject, loading: projectLoading } = useProject()
  const { databaseService, loading: dbLoading } = useDatabase()
  const [seoSettings, setSeoSettings] = useState<SEOSettings | null>(null)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [message, setMessage] = useState<{ type: 'success' | 'error', text: string } | null>(null)

  // 加载SEO设置
  const loadSEOSettings = async () => {
    if (!databaseService || !currentProject) return

    try {
      setLoading(true)
      const settings = await databaseService.getSEOSettings(currentProject.id)
      setSeoSettings(settings)
    } catch (error) {
      console.error('加载SEO设置失败:', error)
      setMessage({ type: 'error', text: '加载SEO设置失败' })
    } finally {
      setLoading(false)
    }
  }

  // 保存SEO设置
  const saveSEOSettings = async () => {
    if (!databaseService || !currentProject) return

    try {
      setSaving(true)
      
      const settingsData = {
        project_id: currentProject.id,
        site_name: seoSettings?.site_name || '',
        site_url: seoSettings?.site_url || '',
        site_description: seoSettings?.site_description || '',
        default_seo_title_template: seoSettings?.default_seo_title_template || '',
        default_seo_description_template: seoSettings?.default_seo_description_template || '',
        default_category: seoSettings?.default_category || '',
        default_tags: seoSettings?.default_tags || []
      }

      if (seoSettings?.id) {
        await databaseService.updateSEOSettings(seoSettings.id, settingsData)
      } else {
        const newSettings = await databaseService.createSEOSettings(settingsData)
        setSeoSettings(newSettings)
      }

      setMessage({ type: 'success', text: 'SEO设置保存成功' })
    } catch (error) {
      console.error('保存SEO设置失败:', error)
      setMessage({ type: 'error', text: '保存SEO设置失败' })
    } finally {
      setSaving(false)
    }
  }

  useEffect(() => {
    if (!dbLoading && databaseService && currentProject) {
      loadSEOSettings()
    }
  }, [databaseService, currentProject, dbLoading])

  // 清除消息
  useEffect(() => {
    if (message) {
      const timer = setTimeout(() => setMessage(null), 3000)
      return () => clearTimeout(timer)
    }
  }, [message])

  if (projectLoading || dbLoading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
          <span className="ml-2">加载中...</span>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            项目设置
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            管理项目配置、数据库连接和SEO设置
          </p>
        </div>

        {/* Message */}
        {message && (
          <div className={`mb-6 p-4 rounded-lg ${
            message.type === 'success' 
              ? 'bg-green-50 text-green-800 border border-green-200' 
              : 'bg-red-50 text-red-800 border border-red-200'
          }`}>
            {message.text}
          </div>
        )}

        <Tabs defaultValue="project" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="project" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              项目信息
            </TabsTrigger>
            <TabsTrigger value="seo" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              SEO设置
            </TabsTrigger>
            <TabsTrigger value="api" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              API配置
            </TabsTrigger>
          </TabsList>

          {/* 项目信息 */}
          <TabsContent value="project">
            <div className="space-y-6">
              <ProjectSelector />
              
              {currentProject && (
                <Card>
                  <CardHeader>
                    <CardTitle>当前项目详情</CardTitle>
                    <CardDescription>
                      查看当前选择项目的详细信息
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>项目ID</Label>
                        <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded text-sm font-mono">
                          {currentProject.id}
                        </div>
                      </div>
                      <div>
                        <Label>项目名称</Label>
                        <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          {currentProject.name}
                        </div>
                      </div>
                    </div>
                    
                    {currentProject.description && (
                      <div>
                        <Label>项目描述</Label>
                        <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          {currentProject.description}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <Label>数据库URL</Label>
                      <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded text-sm font-mono">
                        {currentProject.supabase_url}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        创建时间: {new Date(currentProject.created_at).toLocaleString()}
                      </Badge>
                      <Badge variant="outline">
                        更新时间: {new Date(currentProject.updated_at).toLocaleString()}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          </TabsContent>

          {/* SEO设置 */}
          <TabsContent value="seo">
            <Card>
              <CardHeader>
                <CardTitle>SEO默认设置</CardTitle>
                <CardDescription>
                  配置项目的默认SEO设置，用于博文生成时的参考
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                    <span>加载SEO设置中...</span>
                  </div>
                ) : (
                  <>
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="siteName">站点名称</Label>
                        <Input
                          id="siteName"
                          value={seoSettings?.site_name || ''}
                          onChange={(e) => setSeoSettings(prev => prev ? {...prev, site_name: e.target.value} : null)}
                          placeholder="输入站点名称"
                        />
                      </div>
                      <div>
                        <Label htmlFor="siteUrl">站点URL</Label>
                        <Input
                          id="siteUrl"
                          value={seoSettings?.site_url || ''}
                          onChange={(e) => setSeoSettings(prev => prev ? {...prev, site_url: e.target.value} : null)}
                          placeholder="https://example.com"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="siteDescription">站点描述</Label>
                      <Textarea
                        id="siteDescription"
                        value={seoSettings?.site_description || ''}
                        onChange={(e) => setSeoSettings(prev => prev ? {...prev, site_description: e.target.value} : null)}
                        placeholder="输入站点描述"
                        rows={3}
                      />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="defaultCategory">默认分类</Label>
                        <Input
                          id="defaultCategory"
                          value={seoSettings?.default_category || ''}
                          onChange={(e) => setSeoSettings(prev => prev ? {...prev, default_category: e.target.value} : null)}
                          placeholder="输入默认分类"
                        />
                      </div>
                      <div>
                        <Label htmlFor="defaultTags">默认标签（逗号分隔）</Label>
                        <Input
                          id="defaultTags"
                          value={seoSettings?.default_tags?.join(', ') || ''}
                          onChange={(e) => setSeoSettings(prev => prev ? {...prev, default_tags: e.target.value.split(',').map(tag => tag.trim()).filter(Boolean)} : null)}
                          placeholder="标签1, 标签2, 标签3"
                        />
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="titleTemplate">SEO标题模板</Label>
                      <Input
                        id="titleTemplate"
                        value={seoSettings?.default_seo_title_template || ''}
                        onChange={(e) => setSeoSettings(prev => prev ? {...prev, default_seo_title_template: e.target.value} : null)}
                        placeholder="{title} - {siteName}"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="descriptionTemplate">SEO描述模板</Label>
                      <Textarea
                        id="descriptionTemplate"
                        value={seoSettings?.default_seo_description_template || ''}
                        onChange={(e) => setSeoSettings(prev => prev ? {...prev, default_seo_description_template: e.target.value} : null)}
                        placeholder="输入SEO描述模板"
                        rows={2}
                      />
                    </div>
                    
                    <Button onClick={saveSEOSettings} disabled={saving} className="w-full">
                      {saving ? (
                        <>
                          <RefreshCw className="h-4 w-4 animate-spin mr-2" />
                          保存中...
                        </>
                      ) : (
                        <>
                          <Save className="h-4 w-4 mr-2" />
                          保存SEO设置
                        </>
                      )}
                    </Button>
                  </>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* API配置 */}
          <TabsContent value="api">
            <Card>
              <CardHeader>
                <CardTitle>API配置</CardTitle>
                <CardDescription>
                  查看当前配置的AI API信息
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label>OpenAI API</Label>
                    <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded text-sm">
                      {process.env.NEXT_PUBLIC_OPENAI_API_KEY ? 
                        `已配置 (${process.env.NEXT_PUBLIC_OPENAI_API_KEY.substring(0, 10)}...)` : 
                        '未配置'
                      }
                    </div>
                  </div>
                  
                  <div>
                    <Label>通义千问 API</Label>
                    <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded text-sm">
                      {process.env.NEXT_PUBLIC_QWEN_API_KEY ? 
                        `已配置 (${process.env.NEXT_PUBLIC_QWEN_API_KEY.substring(0, 10)}...)` : 
                        '未配置'
                      }
                    </div>
                  </div>
                </div>
                
                <div className="text-sm text-slate-600 dark:text-slate-400">
                  <p>API密钥通过环境变量配置，请在 .env.local 文件中设置：</p>
                  <ul className="mt-2 space-y-1 list-disc list-inside">
                    <li>OPENAI_API_KEY</li>
                    <li>QWEN_API_KEY</li>
                    <li>QWEN_API_URL</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
