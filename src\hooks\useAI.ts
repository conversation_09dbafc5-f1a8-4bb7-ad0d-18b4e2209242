import { useState } from 'react'

export interface GenerateBlogParams {
  keywords: string[]
  topic?: string
  title?: string
  language: string
  seriesContext?: string
  customPrompt?: string
  aiModel?: 'gpt-4' | 'gpt-3.5-turbo' | 'qwen-plus-latest'
}

export interface GenerateBlogResult {
  title: string
  content: string
  seoTitle: string
  seoDescription: string
  tags: string[]
  category: string
}

export interface GenerateTitleParams {
  keywords: string[]
  topic?: string
  language: string
}

export interface GenerateSEOParams {
  title: string
  content: string
  language: string
}

export interface GenerateSEOResult {
  seoTitle: string
  seoDescription: string
  tags: string[]
}

export interface GenerateAuthorParams {
  topic: string
  language: string
}

export interface GenerateAuthorResult {
  name: string
  bio: string
  avatar?: string
}

export function useAI() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const generateBlog = async (params: GenerateBlogParams): Promise<GenerateBlogResult | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/ai/generate-blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '博文生成失败')
      }

      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '博文生成失败'
      setError(errorMessage)
      console.error('博文生成失败:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const generateTitle = async (params: GenerateTitleParams): Promise<string | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/ai/generate-blog', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '标题生成失败')
      }

      return result.data.title
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '标题生成失败'
      setError(errorMessage)
      console.error('标题生成失败:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const generateSEO = async (params: GenerateSEOParams): Promise<GenerateSEOResult | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/ai/generate-seo', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || 'SEO信息生成失败')
      }

      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'SEO信息生成失败'
      setError(errorMessage)
      console.error('SEO信息生成失败:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  const generateAuthor = async (params: GenerateAuthorParams): Promise<GenerateAuthorResult | null> => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch('/api/ai/generate-author', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(params),
      })

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '作者信息生成失败')
      }

      return result.data
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '作者信息生成失败'
      setError(errorMessage)
      console.error('作者信息生成失败:', err)
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    generateBlog,
    generateTitle,
    generateSEO,
    generateAuthor,
    loading,
    error,
    clearError: () => setError(null)
  }
}
