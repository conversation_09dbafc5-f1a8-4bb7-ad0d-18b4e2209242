import { NextRequest, NextResponse } from 'next/server'
import { aiService } from '@/lib/ai'
import { z } from 'zod'

const generateSEOSchema = z.object({
  title: z.string().min(1, '标题不能为空'),
  content: z.string().min(1, '内容不能为空'),
  language: z.string().default('zh')
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证请求参数
    const validatedData = generateSEOSchema.parse(body)
    
    // 调用AI服务生成SEO信息
    const result = await aiService.generateSEOInfo({
      title: validatedData.title,
      content: validatedData.content,
      language: validatedData.language
    })
    
    return NextResponse.json({
      success: true,
      data: result
    })
    
  } catch (error) {
    console.error('SEO信息生成失败:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: '请求参数无效',
        details: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'SEO信息生成失败'
    }, { status: 500 })
  }
}
