'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { useAI } from '@/hooks/useAI'
import { useProject } from '@/contexts/ProjectContext'
import { PenTool, Wand2, Globe, Tag, Loader2, Plus, X, Sparkles } from 'lucide-react'

// 表单验证schema
const generateFormSchema = z.object({
  keywords: z.array(z.string()).min(1, '至少需要一个关键词'),
  topic: z.string().optional(),
  title: z.string().optional(),
  language: z.string().default('zh'),
  seriesId: z.string().optional(),
  customPrompt: z.string().optional(),
  aiModel: z.enum(['gpt-4', 'gpt-3.5-turbo', 'qwen-plus-latest']).default('gpt-4')
})

type GenerateFormData = z.infer<typeof generateFormSchema>

export default function GeneratePage() {
  const { currentProject } = useProject()
  const { generateBlog, generateTitle, loading, error } = useAI()
  const [keywordInput, setKeywordInput] = useState('')
  const [keywords, setKeywords] = useState<string[]>([])
  const [generatedContent, setGeneratedContent] = useState<any>(null)
  const [step, setStep] = useState<'form' | 'generating' | 'result'>('form')

  const form = useForm<GenerateFormData>({
    resolver: zodResolver(generateFormSchema),
    defaultValues: {
      keywords: [],
      language: 'zh',
      aiModel: 'gpt-4'
    }
  })

  // 添加关键词
  const addKeyword = () => {
    if (keywordInput.trim() && !keywords.includes(keywordInput.trim())) {
      const newKeywords = [...keywords, keywordInput.trim()]
      setKeywords(newKeywords)
      form.setValue('keywords', newKeywords)
      setKeywordInput('')
    }
  }

  // 删除关键词
  const removeKeyword = (keyword: string) => {
    const newKeywords = keywords.filter(k => k !== keyword)
    setKeywords(newKeywords)
    form.setValue('keywords', newKeywords)
  }

  // 生成标题
  const handleGenerateTitle = async () => {
    const formData = form.getValues()
    if (formData.keywords.length === 0) {
      form.setError('keywords', { message: '请先添加关键词' })
      return
    }

    const title = await generateTitle({
      keywords: formData.keywords,
      topic: formData.topic,
      language: formData.language
    })

    if (title) {
      form.setValue('title', title)
    }
  }

  // 生成博文
  const onSubmit = async (data: GenerateFormData) => {
    try {
      setStep('generating')
      
      const result = await generateBlog({
        keywords: data.keywords,
        topic: data.topic,
        title: data.title,
        language: data.language,
        customPrompt: data.customPrompt,
        aiModel: data.aiModel
      })

      if (result) {
        setGeneratedContent(result)
        setStep('result')
      } else {
        setStep('form')
      }
    } catch (err) {
      console.error('生成博文失败:', err)
      setStep('form')
    }
  }

  // 重新生成
  const handleRegenerate = () => {
    setStep('form')
    setGeneratedContent(null)
  }

  if (step === 'generating') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <Loader2 className="h-12 w-12 animate-spin text-blue-600 mb-4" />
              <h2 className="text-xl font-semibold mb-2">AI正在生成博文...</h2>
              <p className="text-slate-600 dark:text-slate-400 text-center">
                这可能需要几分钟时间，请耐心等待
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  if (step === 'result' && generatedContent) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-6 flex items-center justify-between">
            <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
              生成结果
            </h1>
            <div className="space-x-2">
              <Button variant="outline" onClick={handleRegenerate}>
                重新生成
              </Button>
              <Button>
                保存博文
              </Button>
            </div>
          </div>

          <div className="space-y-6">
            {/* 基础信息 */}
            <Card>
              <CardHeader>
                <CardTitle>博文信息</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>标题</Label>
                  <div className="mt-1 p-3 bg-slate-50 dark:bg-slate-800 rounded-lg">
                    <h2 className="text-lg font-semibold">{generatedContent.title}</h2>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>分类</Label>
                    <div className="mt-1">
                      <Badge variant="secondary">{generatedContent.category}</Badge>
                    </div>
                  </div>
                  <div>
                    <Label>标签</Label>
                    <div className="mt-1 flex flex-wrap gap-1">
                      {generatedContent.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline">{tag}</Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* SEO信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5" />
                  SEO信息
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label>SEO标题</Label>
                  <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                    {generatedContent.seoTitle}
                  </div>
                </div>
                <div>
                  <Label>SEO描述</Label>
                  <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                    {generatedContent.seoDescription}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 博文内容 */}
            <Card>
              <CardHeader>
                <CardTitle>博文内容</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none dark:prose-invert">
                  <div className="whitespace-pre-wrap bg-slate-50 dark:bg-slate-800 p-4 rounded-lg">
                    {generatedContent.content}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100 mb-2">
            AI博文生成
          </h1>
          <p className="text-slate-600 dark:text-slate-400">
            输入关键词和话题，让AI为您生成高质量的SEO博文
          </p>
          {currentProject && (
            <div className="mt-2">
              <Badge variant="outline">当前项目: {currentProject.name}</Badge>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 text-red-800 border border-red-200 rounded-lg">
            {error}
          </div>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="basic" className="space-y-6">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="basic">基础设置</TabsTrigger>
                <TabsTrigger value="advanced">高级设置</TabsTrigger>
                <TabsTrigger value="preview">预览</TabsTrigger>
              </TabsList>

              {/* 基础设置 */}
              <TabsContent value="basic">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <PenTool className="h-5 w-5" />
                      基础信息
                    </CardTitle>
                    <CardDescription>
                      设置博文的基本信息和关键词
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 关键词输入 */}
                    <div className="space-y-2">
                      <Label>关键词 *</Label>
                      <div className="flex gap-2">
                        <Input
                          value={keywordInput}
                          onChange={(e) => setKeywordInput(e.target.value)}
                          placeholder="输入关键词"
                          onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                        />
                        <Button type="button" onClick={addKeyword} size="sm">
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {/* 关键词列表 */}
                      {keywords.length > 0 && (
                        <div className="flex flex-wrap gap-2 mt-2">
                          {keywords.map((keyword, index) => (
                            <Badge key={index} variant="secondary" className="flex items-center gap-1">
                              {keyword}
                              <button
                                type="button"
                                onClick={() => removeKeyword(keyword)}
                                className="ml-1 hover:text-red-600"
                              >
                                <X className="h-3 w-3" />
                              </button>
                            </Badge>
                          ))}
                        </div>
                      )}
                      
                      {form.formState.errors.keywords && (
                        <p className="text-sm text-red-600">
                          {form.formState.errors.keywords.message}
                        </p>
                      )}
                    </div>

                    {/* 话题 */}
                    <FormField
                      control={form.control}
                      name="topic"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>话题</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="输入博文话题（可选）" />
                          </FormControl>
                          <FormDescription>
                            话题可以帮助AI更好地理解您想要的内容方向
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 标题 */}
                    <FormField
                      control={form.control}
                      name="title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="flex items-center justify-between">
                            标题
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={handleGenerateTitle}
                              disabled={loading || keywords.length === 0}
                            >
                              {loading ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-1" />
                              ) : (
                                <Wand2 className="h-4 w-4 mr-1" />
                              )}
                              AI生成
                            </Button>
                          </FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="输入博文标题（可选，留空将自动生成）" />
                          </FormControl>
                          <FormDescription>
                            可以手动输入标题，或点击"AI生成"按钮自动生成
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 语言选择 */}
                    <FormField
                      control={form.control}
                      name="language"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>语言</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择语言" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="zh">中文</SelectItem>
                              <SelectItem value="en">English</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 高级设置 */}
              <TabsContent value="advanced">
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5" />
                      高级设置
                    </CardTitle>
                    <CardDescription>
                      配置AI模型和自定义提示词
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* AI模型选择 */}
                    <FormField
                      control={form.control}
                      name="aiModel"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>AI模型</FormLabel>
                          <Select onValueChange={field.onChange} defaultValue={field.value}>
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="选择AI模型" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="gpt-4">GPT-4 (推荐)</SelectItem>
                              <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                              <SelectItem value="qwen-plus-latest">通义千问 Plus</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormDescription>
                            不同模型有不同的特点，GPT-4质量最高但速度较慢
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 自定义提示词 */}
                    <FormField
                      control={form.control}
                      name="customPrompt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>自定义提示词</FormLabel>
                          <FormControl>
                            <Textarea
                              {...field}
                              placeholder="输入自定义提示词来指导AI生成特定风格的内容（可选）"
                              rows={4}
                            />
                          </FormControl>
                          <FormDescription>
                            自定义提示词可以让AI按照您的特定要求生成内容
                          </FormDescription>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </CardContent>
                </Card>
              </TabsContent>

              {/* 预览 */}
              <TabsContent value="preview">
                <Card>
                  <CardHeader>
                    <CardTitle>生成预览</CardTitle>
                    <CardDescription>
                      确认生成参数后点击生成按钮
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label>关键词</Label>
                        <div className="mt-1 flex flex-wrap gap-1">
                          {keywords.map((keyword, index) => (
                            <Badge key={index} variant="outline">{keyword}</Badge>
                          ))}
                        </div>
                      </div>
                      <div>
                        <Label>语言</Label>
                        <div className="mt-1">
                          <Badge variant="secondary">
                            {form.watch('language') === 'zh' ? '中文' : 'English'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    {form.watch('topic') && (
                      <div>
                        <Label>话题</Label>
                        <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          {form.watch('topic')}
                        </div>
                      </div>
                    )}
                    
                    {form.watch('title') && (
                      <div>
                        <Label>标题</Label>
                        <div className="mt-1 p-2 bg-slate-50 dark:bg-slate-800 rounded">
                          {form.watch('title')}
                        </div>
                      </div>
                    )}
                    
                    <div>
                      <Label>AI模型</Label>
                      <div className="mt-1">
                        <Badge variant="outline">{form.watch('aiModel')}</Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* 生成按钮 */}
            <div className="flex justify-end">
              <Button 
                type="submit" 
                size="lg" 
                disabled={loading || keywords.length === 0}
                className="min-w-[200px]"
              >
                {loading ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    生成中...
                  </>
                ) : (
                  <>
                    <Sparkles className="h-4 w-4 mr-2" />
                    生成博文
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  )
}
