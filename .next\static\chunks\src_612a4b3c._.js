(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/lib/database.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "SupabaseDatabaseService": ()=>SupabaseDatabaseService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
;
class SupabaseDatabaseService {
    // 项目管理
    async getProjects() {
        const { data, error } = await this.supabase.from('projects').select('*').order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getProject(id) {
        const { data, error } = await this.supabase.from('projects').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null // Not found
            ;
            throw error;
        }
        return data;
    }
    async createProject(project) {
        const { data, error } = await this.supabase.from('projects').insert(project).select().single();
        if (error) throw error;
        return data;
    }
    async updateProject(id, updates) {
        const { data, error } = await this.supabase.from('projects').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteProject(id) {
        const { error } = await this.supabase.from('projects').delete().eq('id', id);
        if (error) throw error;
    }
    // 博文系列
    async getBlogSeries(projectId) {
        const { data, error } = await this.supabase.from('blog_series').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getBlogSeriesById(id) {
        const { data, error } = await this.supabase.from('blog_series').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createBlogSeries(series) {
        const { data, error } = await this.supabase.from('blog_series').insert(series).select().single();
        if (error) throw error;
        return data;
    }
    async updateBlogSeries(id, updates) {
        const { data, error } = await this.supabase.from('blog_series').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteBlogSeries(id) {
        const { error } = await this.supabase.from('blog_series').delete().eq('id', id);
        if (error) throw error;
    }
    // 作者管理
    async getAuthors(projectId) {
        const { data, error } = await this.supabase.from('authors').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        });
        if (error) throw error;
        return data || [];
    }
    async getAuthor(id) {
        const { data, error } = await this.supabase.from('authors').select('*').eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createAuthor(author) {
        const { data, error } = await this.supabase.from('authors').insert(author).select().single();
        if (error) throw error;
        return data;
    }
    async updateAuthor(id, updates) {
        const { data, error } = await this.supabase.from('authors').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteAuthor(id) {
        const { error } = await this.supabase.from('authors').delete().eq('id', id);
        if (error) throw error;
    }
    // 博文管理
    async getBlogPosts(projectId, filters) {
        let query = this.supabase.from('blog_posts').select("\n        *,\n        series:blog_series(id, name),\n        author:authors(id, name)\n      ").eq('project_id', projectId);
        if (filters === null || filters === void 0 ? void 0 : filters.status) {
            query = query.eq('status', filters.status);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.seriesId) {
            query = query.eq('series_id', filters.seriesId);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.authorId) {
            query = query.eq('author_id', filters.authorId);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.limit) {
            query = query.limit(filters.limit);
        }
        if (filters === null || filters === void 0 ? void 0 : filters.offset) {
            query = query.range(filters.offset, filters.offset + (filters.limit || 10) - 1);
        }
        query = query.order('created_at', {
            ascending: false
        });
        const { data, error } = await query;
        if (error) throw error;
        return data || [];
    }
    async getBlogPost(id) {
        const { data, error } = await this.supabase.from('blog_posts').select("\n        *,\n        series:blog_series(id, name),\n        author:authors(id, name)\n      ").eq('id', id).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createBlogPost(post) {
        // 计算字数和阅读时间
        const wordCount = post.content.length;
        const readingTime = Math.ceil(wordCount / 200) // 假设每分钟200字
        ;
        const postWithMeta = {
            ...post,
            word_count: wordCount,
            reading_time: readingTime
        };
        const { data, error } = await this.supabase.from('blog_posts').insert(postWithMeta).select().single();
        if (error) throw error;
        return data;
    }
    async updateBlogPost(id, updates) {
        // 如果更新了内容，重新计算字数和阅读时间
        if (updates.content) {
            const wordCount = updates.content.length;
            const readingTime = Math.ceil(wordCount / 200);
            updates.word_count = wordCount;
            updates.reading_time = readingTime;
        }
        const { data, error } = await this.supabase.from('blog_posts').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    async deleteBlogPost(id) {
        const { error } = await this.supabase.from('blog_posts').delete().eq('id', id);
        if (error) throw error;
    }
    // SEO设置
    async getSEOSettings(projectId) {
        const { data, error } = await this.supabase.from('seo_settings').select('*').eq('project_id', projectId).single();
        if (error) {
            if (error.code === 'PGRST116') return null;
            throw error;
        }
        return data;
    }
    async createSEOSettings(settings) {
        const { data, error } = await this.supabase.from('seo_settings').insert(settings).select().single();
        if (error) throw error;
        return data;
    }
    async updateSEOSettings(id, updates) {
        const { data, error } = await this.supabase.from('seo_settings').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    // 生成历史
    async getGenerationHistory(projectId) {
        let limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 50;
        const { data, error } = await this.supabase.from('generation_history').select('*').eq('project_id', projectId).order('created_at', {
            ascending: false
        }).limit(limit);
        if (error) throw error;
        return data || [];
    }
    async createGenerationHistory(history) {
        const { data, error } = await this.supabase.from('generation_history').insert(history).select().single();
        if (error) throw error;
        return data;
    }
    async updateGenerationHistory(id, updates) {
        const { data, error } = await this.supabase.from('generation_history').update(updates).eq('id', id).select().single();
        if (error) throw error;
        return data;
    }
    constructor(supabase){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "supabase", void 0);
        this.supabase = supabase;
    }
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/supabase.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "defaultProjects": ()=>defaultProjects,
    "getProjectSupabase": ()=>getProjectSupabase,
    "supabase": ()=>supabase,
    "tarotSeoSupabase": ()=>tarotSeoSupabase
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@supabase/supabase-js/dist/module/index.js [app-client] (ecmascript) <locals>");
;
// AI博文数据库 (主数据库)
const supabaseUrl = ("TURBOPACK compile-time value", "https://ohcnehqjrqzjlgbmjcck.supabase.co");
const supabaseAnonKey = ("TURBOPACK compile-time value", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9oY25laHFqcnF6amxnYm1qY2NrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQyNjIzMzYsImV4cCI6MjA2OTgzODMzNn0.sKrSvG0qQEmQqgnoqSv-5EmvWRQ4FQAvSN2Yi_MMo7o");
const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(supabaseUrl, supabaseAnonKey);
// tarot-seo项目数据库
const tarotSeoUrl = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TAROT_SEO_SUPABASE_URL;
const tarotSeoAnonKey = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TAROT_SEO_SUPABASE_ANON_KEY;
const tarotSeoSupabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(tarotSeoUrl, tarotSeoAnonKey);
const defaultProjects = [
    {
        id: 'tarot-seo',
        name: 'Tarot SEO',
        supabaseUrl: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TAROT_SEO_SUPABASE_URL,
        supabaseAnonKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TAROT_SEO_SUPABASE_ANON_KEY,
        supabaseServiceKey: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].env.TAROT_SEO_SUPABASE_SERVICE_ROLE_KEY
    }
];
function getProjectSupabase(projectId) {
    const project = defaultProjects.find((p)=>p.id === projectId);
    if (!project) {
        throw new Error("Project ".concat(projectId, " not found"));
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$supabase$2f$supabase$2d$js$2f$dist$2f$module$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createClient"])(project.supabaseUrl, project.supabaseAnonKey);
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/contexts/ProjectContext.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ProjectProvider": ()=>ProjectProvider,
    "useDatabase": ()=>useDatabase,
    "useProject": ()=>useProject
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/supabase.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature();
'use client';
;
;
;
const ProjectContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function ProjectProvider(param) {
    let { children } = param;
    _s();
    const [currentProject, setCurrentProject] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [projects, setProjects] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [databaseService, setDatabaseService] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // 初始化项目列表
    const refreshProjects = async ()=>{
        try {
            setLoading(true);
            setError(null);
            // 从默认配置加载项目（后续可以从主数据库加载）
            const projectList = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultProjects"].map((p)=>({
                    id: p.id,
                    name: p.name,
                    description: "".concat(p.name, " 项目"),
                    supabase_url: p.supabaseUrl,
                    supabase_anon_key: p.supabaseAnonKey,
                    supabase_service_key: p.supabaseServiceKey,
                    created_at: new Date().toISOString(),
                    updated_at: new Date().toISOString()
                }));
            setProjects(projectList);
            // 如果没有当前项目，设置默认项目
            if (!currentProject && projectList.length > 0) {
                const defaultProject = projectList.find((p)=>p.id === 'tarot-seo') || projectList[0];
                setCurrentProject(defaultProject);
                // 创建数据库服务实例
                const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProjectSupabase"])(defaultProject.id);
                setDatabaseService(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseDatabaseService"](supabase));
            }
        } catch (err) {
            setError(err instanceof Error ? err.message : '加载项目失败');
            console.error('加载项目失败:', err);
        } finally{
            setLoading(false);
        }
    };
    // 切换项目
    const switchProject = (projectId)=>{
        const project = projects.find((p)=>p.id === projectId);
        if (!project) {
            setError("项目 ".concat(projectId, " 不存在"));
            return;
        }
        try {
            setCurrentProject(project);
            // 创建新的数据库服务实例
            const supabase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$supabase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getProjectSupabase"])(projectId);
            setDatabaseService(new __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SupabaseDatabaseService"](supabase));
            // 保存到localStorage
            localStorage.setItem('currentProjectId', projectId);
            setError(null);
        } catch (err) {
            setError(err instanceof Error ? err.message : '切换项目失败');
            console.error('切换项目失败:', err);
        }
    };
    // 初始化
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProjectProvider.useEffect": ()=>{
            const initializeProject = {
                "ProjectProvider.useEffect.initializeProject": async ()=>{
                    await refreshProjects();
                    // 从localStorage恢复上次选择的项目
                    const savedProjectId = localStorage.getItem('currentProjectId');
                    if (savedProjectId && projects.length > 0) {
                        const savedProject = projects.find({
                            "ProjectProvider.useEffect.initializeProject.savedProject": (p)=>p.id === savedProjectId
                        }["ProjectProvider.useEffect.initializeProject.savedProject"]);
                        if (savedProject) {
                            switchProject(savedProjectId);
                        }
                    }
                }
            }["ProjectProvider.useEffect.initializeProject"];
            initializeProject();
        }
    }["ProjectProvider.useEffect"], []);
    const value = {
        currentProject,
        projects,
        databaseService,
        switchProject,
        refreshProjects,
        loading,
        error
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(ProjectContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/ProjectContext.tsx",
        lineNumber: 123,
        columnNumber: 5
    }, this);
}
_s(ProjectProvider, "WWXviyB/Wxo718jrqm8EwgNU/CI=");
_c = ProjectProvider;
function useProject() {
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(ProjectContext);
    if (context === undefined) {
        throw new Error('useProject must be used within a ProjectProvider');
    }
    return context;
}
_s1(useProject, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
function useDatabase() {
    _s2();
    const { databaseService, currentProject, loading } = useProject();
    if (loading) {
        return {
            databaseService: null,
            currentProject: null,
            loading: true
        };
    }
    if (!databaseService || !currentProject) {
        throw new Error('数据库服务未初始化或没有选择项目');
    }
    return {
        databaseService,
        currentProject,
        loading: false
    };
}
_s2(useDatabase, "xXhGngVS/NKPkLsq6Gs3d+XSwyM=", false, function() {
    return [
        useProject
    ];
});
var _c;
__turbopack_context__.k.register(_c, "ProjectProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_612a4b3c._.js.map