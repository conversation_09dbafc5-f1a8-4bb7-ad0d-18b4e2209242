{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/contexts/ProjectContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ProjectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProjectProvider() from the server but ProjectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx <module evaluation>\",\n    \"ProjectProvider\",\n);\nexport const useDatabase = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDatabase() from the server but useDatabase is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx <module evaluation>\",\n    \"useDatabase\",\n);\nexport const useProject = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProject() from the server but useProject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx <module evaluation>\",\n    \"useProject\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,iEACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,iEACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,iEACA", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/contexts/ProjectContext.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ProjectProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ProjectProvider() from the server but ProjectProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx\",\n    \"ProjectProvider\",\n);\nexport const useDatabase = registerClientReference(\n    function() { throw new Error(\"Attempted to call useDatabase() from the server but useDatabase is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx\",\n    \"useDatabase\",\n);\nexport const useProject = registerClientReference(\n    function() { throw new Error(\"Attempted to call useProject() from the server but useProject is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/contexts/ProjectContext.tsx\",\n    \"useProject\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6CACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,6CACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6CACA", "debugId": null}}, {"offset": {"line": 99, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/src/app/layout.tsx"], "sourcesContent": ["import type { Metada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ProjectProvider } from \"@/contexts/ProjectContext\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"AI博文自动生成系统\",\n  description: \"智能化SEO博文创作平台，支持多项目管理、系列博文生成、AI内容优化\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"zh-CN\">\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased`}\n      >\n        <ProjectProvider>\n          {children}\n        </ProjectProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;;;;;;AAYO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,YAAY,CAAC;sBAEpE,cAAA,8OAAC,kIAAA,CAAA,kBAAe;0BACb;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/blog-auto-v2/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}